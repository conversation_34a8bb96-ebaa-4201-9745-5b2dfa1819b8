// ============================================================================
// DRAFT QUEUE COMPONENT
// ============================================================================

/**
 * Draft Queue Component
 * Handles queue creation mode, draft queue management, and queue building functionality
 * Manages the temporary queue that users build before saving as a personal queue
 */

// Draft queue state
let draftQueue = [];
let isInCreationMode = false;

/**
 * Get current draft queue
 * @returns {Array} Array of video objects in draft queue
 */
function getDraftQueue() {
  return [...draftQueue];
}

/**
 * Set draft queue
 * @param {Array} videos - Array of video objects
 */
function setDraftQueue(videos) {
  draftQueue = Array.isArray(videos) ? [...videos] : [];
  updateDraftQueueDisplay();
  saveDraftQueueToStorage();
}

/**
 * Add video to draft queue
 * @param {Object} video - Video object to add
 * @returns {boolean} True if video was added successfully
 */
function addVideoToDraftQueue(video) {
  if (!video || !video.id) {
    console.log('Invalid video object');
    return false;
  }

  // Check if video already exists in draft queue
  const existingIndex = draftQueue.findIndex(v => v.id === video.id);
  if (existingIndex !== -1) {
    console.log('Video already in draft queue');
    
    // Show notification if available
    if (typeof showNotification === 'function') {
      showNotification(`"${video.title}" is already in your draft queue`, 'warning');
    }
    
    return false;
  }

  // Add video to draft queue
  draftQueue.push(video);
  updateDraftQueueDisplay();
  saveDraftQueueToStorage();

  console.log(`➕ Added to draft queue: ${video.title}`);
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification(`Added "${video.title}" to draft queue`, 'success');
  }

  return true;
}

/**
 * Remove video from draft queue
 * @param {number} index - Index of video to remove
 * @returns {boolean} True if video was removed successfully
 */
function removeVideoFromDraftQueue(index) {
  if (index < 0 || index >= draftQueue.length) {
    console.log('Invalid draft queue index:', index);
    return false;
  }

  const removedVideo = draftQueue.splice(index, 1)[0];
  updateDraftQueueDisplay();
  saveDraftQueueToStorage();

  console.log(`➖ Removed from draft queue: ${removedVideo.title}`);
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification(`Removed "${removedVideo.title}" from draft queue`, 'info');
  }

  return true;
}

/**
 * Clear draft queue
 */
function clearDraftQueue() {
  if (draftQueue.length === 0) {
    console.log('Draft queue is already empty');
    return;
  }

  const confirmMessage = `Are you sure you want to clear the draft queue?\n\nThis will remove all ${draftQueue.length} video(s) from your draft.`;
  if (!confirm(confirmMessage)) {
    return;
  }

  draftQueue = [];
  updateDraftQueueDisplay();
  saveDraftQueueToStorage();

  console.log('🗑️ Draft queue cleared');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Draft queue cleared', 'info');
  }
}

/**
 * Check if currently in queue creation mode
 * @returns {boolean} True if in creation mode
 */
function isInQueueCreationMode() {
  return isInCreationMode;
}

/**
 * Enter queue creation mode
 */
function enterQueueCreationMode() {
  isInCreationMode = true;
  
  // Update UI to show creation mode
  updateCreationModeUI(true);
  
  // Load draft queue from storage
  loadDraftQueueFromStorage();
  
  console.log('📝 Entered queue creation mode');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Queue creation mode activated', 'info');
  }
}

/**
 * Exit queue creation mode
 */
function exitQueueCreationMode() {
  isInCreationMode = false;
  
  // Update UI to hide creation mode
  updateCreationModeUI(false);
  
  // Clear draft queue
  draftQueue = [];
  updateDraftQueueDisplay();
  clearDraftQueueFromStorage();
  
  console.log('❌ Exited queue creation mode');
  
  // Show notification if available
  if (typeof showNotification === 'function') {
    showNotification('Queue creation mode deactivated', 'info');
  }
}

/**
 * Update creation mode UI
 * @param {boolean} isCreationMode - Whether creation mode is active
 */
function updateCreationModeUI(isCreationMode) {
  // Elements that should be hidden in creation mode
  const newQueueSection = document.getElementById('new-queue-section');

  // Elements that should be shown in creation mode
  const draftSearchSection = document.getElementById('draft-search-section');
  const queueCreationForm = document.getElementById('queue-creation-form');
  const draftQueueContainer = document.getElementById('draft-queue-container');

  console.log('🎨 Updating creation mode UI:', isCreationMode);

  // Toggle new queue section (hide in creation mode)
  if (newQueueSection) {
    newQueueSection.style.display = isCreationMode ? 'none' : 'block';
    console.log('📝 New queue section:', isCreationMode ? 'hidden' : 'shown');
  } else {
    console.warn('⚠️ New queue section element not found');
  }

  // Toggle draft search section (show in creation mode)
  if (draftSearchSection) {
    draftSearchSection.style.display = isCreationMode ? 'block' : 'none';
    console.log('🔍 Draft search section:', isCreationMode ? 'shown' : 'hidden');
  } else {
    console.warn('⚠️ Draft search section element not found');
  }

  // Toggle queue creation form (show in creation mode)
  if (queueCreationForm) {
    queueCreationForm.style.display = isCreationMode ? 'block' : 'none';
    console.log('📋 Queue creation form:', isCreationMode ? 'shown' : 'hidden');
  } else {
    console.warn('⚠️ Queue creation form element not found');
  }

  // Toggle draft queue container (show in creation mode)
  if (draftQueueContainer) {
    draftQueueContainer.style.display = isCreationMode ? 'block' : 'none';
    console.log('📦 Draft queue container:', isCreationMode ? 'shown' : 'hidden');
  } else {
    console.warn('⚠️ Draft queue container element not found');
  }

  // Update search results to show "Add to Draft" buttons
  if (typeof updateSearchResultsForCreationMode === 'function') {
    updateSearchResultsForCreationMode(isCreationMode);
  }

  // Update navigation if needed
  if (typeof updateNavigationForCreationMode === 'function') {
    updateNavigationForCreationMode(isCreationMode);
  }
}

/**
 * Update draft queue display
 */
function updateDraftQueueDisplay() {
  const draftContainer = document.getElementById('draft-queue-list');
  const draftCount = document.getElementById('draft-queue-count');
  
  if (draftCount) {
    draftCount.textContent = draftQueue.length;
  }

  if (!draftContainer) return;

  if (draftQueue.length === 0) {
    draftContainer.innerHTML = `
      <div class="draft-queue-empty">
        <div class="empty-icon">🎵</div>
        <p>No videos in draft queue</p>
        <p>Search and add videos to build your queue!</p>
      </div>
    `;
    return;
  }

  let draftHTML = '';
  draftQueue.forEach((video, index) => {
    draftHTML += createDraftQueueItemHTML(video, index);
  });

  draftContainer.innerHTML = draftHTML;

  // Add event listeners to draft queue items
  addDraftQueueEventListeners();
}

/**
 * Create HTML for a draft queue item
 * @param {Object} video - Video object
 * @param {number} index - Video index in draft queue
 * @returns {string} HTML string for draft queue item
 */
function createDraftQueueItemHTML(video, index) {
  return `
    <div class="draft-queue-item" data-index="${index}">
      <div class="draft-item-thumbnail">
        <img src="${escapeHtml(video.thumbnail)}" alt="${escapeHtml(video.title)}" loading="lazy" />
        <div class="draft-item-number">${index + 1}</div>
      </div>
      <div class="draft-item-info">
        <div class="draft-item-title" title="${escapeHtml(video.title)}">${escapeHtml(video.title)}</div>
        <div class="draft-item-meta">
          ${formatDuration(video.duration)} • ${escapeHtml(video.channel || 'YouTube')}
        </div>
      </div>
      <div class="draft-item-actions">
        <button class="draft-item-btn remove-btn" data-index="${index}" title="Remove from draft queue">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>
  `;
}

/**
 * Add event listeners to draft queue items
 */
function addDraftQueueEventListeners() {
  // Remove button listeners
  document.querySelectorAll('.draft-item-btn.remove-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const index = parseInt(this.getAttribute('data-index'));
      removeVideoFromDraftQueue(index);
    });
  });

  // Draft item click listeners (for reordering in future)
  document.querySelectorAll('.draft-queue-item').forEach(item => {
    item.addEventListener('click', function(e) {
      // Only handle clicks on the item itself, not buttons
      if (e.target.closest('.draft-item-actions')) {
        return;
      }
      
      // Future: implement drag & drop reordering
      console.log('Draft item clicked:', this.dataset.index);
    });
  });
}

/**
 * Save draft queue to local storage
 */
function saveDraftQueueToStorage() {
  try {
    localStorage.setItem('draftQueue', JSON.stringify(draftQueue));
  } catch (error) {
    console.warn('Could not save draft queue to storage:', error);
  }
}

/**
 * Load draft queue from local storage
 */
function loadDraftQueueFromStorage() {
  try {
    const saved = localStorage.getItem('draftQueue');
    if (saved) {
      const parsed = JSON.parse(saved);
      if (Array.isArray(parsed)) {
        draftQueue = parsed;
        updateDraftQueueDisplay();
        console.log('📂 Loaded draft queue from storage:', draftQueue.length, 'videos');
      }
    }
  } catch (error) {
    console.warn('Could not load draft queue from storage:', error);
    draftQueue = [];
  }
}

/**
 * Clear draft queue from local storage
 */
function clearDraftQueueFromStorage() {
  try {
    localStorage.removeItem('draftQueue');
  } catch (error) {
    console.warn('Could not clear draft queue from storage:', error);
  }
}

/**
 * Get draft queue statistics
 * @returns {Object} Draft queue statistics
 */
function getDraftQueueStats() {
  if (draftQueue.length === 0) {
    return {
      count: 0,
      totalDuration: 0,
      formattedDuration: '0:00'
    };
  }

  const totalDuration = draftQueue.reduce((total, video) => total + (video.duration || 0), 0);

  return {
    count: draftQueue.length,
    totalDuration,
    formattedDuration: formatDuration(totalDuration)
  };
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Initialize draft queue component
 */
function initializeDraftQueue() {
  // Load draft queue from storage if in creation mode
  if (isInCreationMode) {
    loadDraftQueueFromStorage();
  }
  
  console.log('📝 Draft queue component initialized');
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeDraftQueue);
} else {
  initializeDraftQueue();
}

// Export functions for global access
window.getDraftQueue = getDraftQueue;
window.setDraftQueue = setDraftQueue;
window.addVideoToDraftQueue = addVideoToDraftQueue;
window.removeVideoFromDraftQueue = removeVideoFromDraftQueue;
window.clearDraftQueue = clearDraftQueue;
window.isInQueueCreationMode = isInQueueCreationMode;
window.enterQueueCreationMode = enterQueueCreationMode;
window.exitQueueCreationMode = exitQueueCreationMode;
window.getDraftQueueStats = getDraftQueueStats;

console.log('✅ Draft Queue component loaded');
