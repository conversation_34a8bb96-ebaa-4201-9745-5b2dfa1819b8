// ============================================================================
// UI INTERACTIONS CONTROLLER
// ============================================================================

/**
 * Main UI Interactions Controller
 * Handles global UI interactions, input handling, notifications, and keyboard shortcuts
 * Coordinates between different UI components and provides unified interaction patterns
 */

// Search debounce settings
const SEARCH_DEBOUNCE_DELAY = 500; // 500ms delay
let searchTimeout;

/**
 * Set loading state for save queue button
 * @param {HTMLElement} button - The button element
 * @param {boolean} isLoading - Whether to show loading state
 */
function setSaveQueueButtonLoading(button, isLoading) {
  if (!button) return;

  if (isLoading) {
    // Store original content
    button.dataset.originalContent = button.innerHTML;

    // Disable button and show loading state
    button.disabled = true;
    button.classList.add('loading');

    // Replace content with loading spinner
    button.innerHTML = `
      <svg class="loading-spinner" width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z" opacity=".25"/>
        <path d="M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z"/>
      </svg>
      Saving...
    `;
  } else {
    // Restore original state
    button.disabled = false;
    button.classList.remove('loading');

    // Restore original content
    if (button.dataset.originalContent) {
      button.innerHTML = button.dataset.originalContent;
      delete button.dataset.originalContent;
    }
  }
}

/**
 * Show message to user (basic version, can be overridden by enhanced UX)
 * @param {string} message - Message text
 * @param {string} type - Message type ('success', 'error', 'info')
 */
function showMessage(message, type = 'info') {
  const messageContainer = document.getElementById('message-container');
  if (!messageContainer) return;

  messageContainer.innerHTML = `<div class="${type}">${message}</div>`;

  // Auto-hide success messages after 5 seconds
  if (type === 'success') {
    setTimeout(() => {
      const currentMessage = messageContainer.querySelector(`.${type}`);
      if (currentMessage && currentMessage.textContent === message) {
        messageContainer.innerHTML = '';
      }
    }, 5000);
  }
}

// Make showMessage globally available immediately
window.showMessage = showMessage;

/**
 * Automatic search with debouncing
 * @param {string} query - Search query
 */
function performAutoSearch(query) {
  console.log('🔍 performAutoSearch called with:', query);
  const searchResults = document.getElementById('search-results');
  if (!searchResults) {
    console.error('❌ Search results container not found in performAutoSearch');
    return;
  }

  if (!query) {
    console.log('📝 Empty query, clearing search results');
    // Clear search results if input is empty
    searchResults.innerHTML = '';
    searchResults.classList.add('empty');
    return;
  }

  if (isYouTubeURL(query)) {
    console.log('🔗 Query is a YouTube URL, not searching');
    // Don't search if it's a URL
    searchResults.innerHTML = '';
    searchResults.classList.add('empty');
    return;
  }

  console.log('🚀 Triggering YouTube search for:', query);
  // Perform search
  searchYouTubeVideos(query);
}

/**
 * Initialize unified input functionality
 */
function initializeUnifiedInput() {
  console.log('🎛️ Initializing unified input...');
  const unifiedInput = document.getElementById('unified-input');
  const clearSearchBtn = document.getElementById('clear-search-btn');
  const searchResults = document.getElementById('search-results');

  console.log('📝 Elements found:', {
    unifiedInput: !!unifiedInput,
    clearSearchBtn: !!clearSearchBtn,
    searchResults: !!searchResults
  });

  if (!unifiedInput) {
    console.error('❌ Required input element not found');
    return;
  }

  // Auto-add YouTube URLs when detected
  function handleYouTubeURL(input) {
    if (isYouTubeURL(input)) {
      const videoId = extractVideoId(input);
      if (!videoId) {
        console.log('Invalid YouTube URL:', input);
        return false;
      }
      addVideoToQueue(videoId);
      unifiedInput.value = '';
      return true;
    }
    return false;
  }

  // Input change handler with debouncing and clear button visibility
  unifiedInput.addEventListener('input', function() {
    const query = this.value.trim();
    console.log('⌨️ Input changed:', query);

    // Show/hide clear button based on input content
    if (clearSearchBtn) {
      if (query.length > 0) {
        clearSearchBtn.classList.add('visible');
      } else {
        clearSearchBtn.classList.remove('visible');
      }
    }

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search (only for non-URLs)
    if (query && !isYouTubeURL(query)) {
      searchTimeout = setTimeout(() => {
        console.log('⏰ Debounce timeout triggered, calling performAutoSearch');
        performAutoSearch(query);
      }, SEARCH_DEBOUNCE_DELAY);
    } else if (!query) {
      // Clear search results when input is empty
      if (searchResults) {
        searchResults.innerHTML = '';
        searchResults.classList.add('empty');
      }
    }
  });

  // Enter key handling - auto-add YouTube URLs
  unifiedInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      const input = this.value.trim();
      if (input) {
        handleYouTubeURL(input);
      }
    }
  });

  // Paste event handling - auto-add YouTube URLs
  unifiedInput.addEventListener('paste', function(e) {
    // Small delay to let the paste complete
    setTimeout(() => {
      const input = this.value.trim();
      if (input) {
        handleYouTubeURL(input);
      }
    }, 10);
  });

  // Clear search functionality
  if (clearSearchBtn) {
    clearSearchBtn.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      // Clear input
      unifiedInput.value = '';

      // Hide clear button
      clearSearchBtn.classList.remove('visible');

      // Clear search results
      if (searchResults) {
        searchResults.innerHTML = '';
        searchResults.classList.add('empty');
      }

      // Focus back on input
      unifiedInput.focus();
    });
  }

  console.log('✅ Unified input initialized');
}

/**
 * Initialize player controls
 */
function initializePlayerControls() {
  const playBtn = document.getElementById('play-btn');
  const clearQueueBtn = document.getElementById('clear-queue-btn');
  const prevBtn = document.getElementById('prev-btn');
  const nextBtn = document.getElementById('next-btn');

  // Header controls
  const headerPrevBtn = document.getElementById('header-prev-btn');
  const headerNextBtn = document.getElementById('header-next-btn');

  // Play button
  if (playBtn) {
    playBtn.addEventListener('click', function() {
      window.playQueue();
    });
  }

  // Clear queue button (header)
  if (clearQueueBtn) {
    clearQueueBtn.addEventListener('click', function() {
      // Show confirmation dialog before clearing
      const confirmClear = confirm('This will clear your entire queue. Continue?');
      if (confirmClear) {
        window.clearQueue();
      }
    });
  }

  // Previous button (original)
  if (prevBtn) {
    prevBtn.addEventListener('click', function() {
      window.previousVideo();
    });
  }

  // Next button (original)
  if (nextBtn) {
    nextBtn.addEventListener('click', function() {
      window.nextVideo();
    });
  }

  // Header Previous button
  if (headerPrevBtn) {
    headerPrevBtn.addEventListener('click', function() {
      window.previousVideo();
    });
  }

  // Header Next button
  if (headerNextBtn) {
    headerNextBtn.addEventListener('click', function() {
      window.nextVideo();
    });
  }

  console.log('✅ Player controls initialized');
}

/**
 * Initialize queue sharing controls
 */
function initializeQueueSharing() {
  const shareQueueBtn = document.getElementById('share-queue-btn');
  const loadQueueBtn = document.getElementById('load-queue-btn');
  const queueIdInput = document.getElementById('queue-id-input');

  // Share queue button
  if (shareQueueBtn) {
    shareQueueBtn.addEventListener('click', async function() {
      if (getVideoQueue().length === 0) {
        console.log('Cannot share empty queue');
        return;
      }

      // Disable button during operation
      shareQueueBtn.disabled = true;
      shareQueueBtn.textContent = 'Sharing...';

      try {
        const queueId = await saveQueueToFirebase();
        if (queueId) {
          // Save the queue ID for the link display
          if (typeof setCurrentQueueId === 'function') {
            setCurrentQueueId(queueId);
          }

          // Generate the full queue link
          const queueLink = `${window.location.origin}${window.location.pathname}?q=${queueId}`;

          // Copy the full link to clipboard
          try {
            await navigator.clipboard.writeText(queueLink);
            console.log('Queue link copied to clipboard');
          } catch (clipboardError) {
            console.warn('Could not copy link to clipboard:', clipboardError);
            console.log('Queue shared successfully');
          }
        }
      } finally {
        // Re-enable button
        shareQueueBtn.disabled = false;
        shareQueueBtn.textContent = 'Share';
      }
    });
  }

  // Load queue button
  if (loadQueueBtn && queueIdInput) {
    loadQueueBtn.addEventListener('click', function() {
      // Toggle input visibility
      if (queueIdInput.style.display === 'none') {
        queueIdInput.style.display = 'inline-block';
        queueIdInput.focus();
        loadQueueBtn.textContent = '📥 Load';
      } else {
        const queueId = queueIdInput.value.trim();
        if (queueId) {
          loadQueueFromFirebase(queueId).then(success => {
            if (success) {
              queueIdInput.value = '';
              queueIdInput.style.display = 'none';
              loadQueueBtn.textContent = '📥 Load';
            }
          });
        } else {
          console.log('No Queue ID entered');
        }
      }
    });

    // Handle Enter key in queue ID input
    queueIdInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        loadQueueBtn.click();
      }
    });
  }

  console.log('✅ Queue sharing initialized');
}

/**
 * Initialize keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
  document.addEventListener('keydown', function(e) {
    // Space bar to play/pause (when not in input field)
    if (e.code === 'Space' && !['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
      e.preventDefault();
      const player = getPlayer();
      if (player && typeof player.getPlayerState === 'function') {
        const state = player.getPlayerState();
        if (state === 1) { // Playing
          player.pauseVideo();
        } else if (state === 2) { // Paused
          player.playVideo();
        }
      }
    }

    // Arrow keys for navigation
    if (e.code === 'ArrowLeft' && e.ctrlKey) {
      e.preventDefault();
      previousVideo();
    }

    if (e.code === 'ArrowRight' && e.ctrlKey) {
      e.preventDefault();
      nextVideo();
    }
  });

  console.log('✅ Keyboard shortcuts initialized');
}

/**
 * Check URL for queue parameter and auto-load
 */
function checkURLForQueue() {
  const urlParams = new URLSearchParams(window.location.search);
  const queueId = urlParams.get('q');

  if (queueId) {
    console.log('🔗 Found queue ID in URL:', queueId);
    // Auto-load the queue from the URL
    if (typeof loadQueueFromFirebase === 'function') {
      loadQueueFromFirebase(queueId);
    }
  }
}

/**
 * Initialize queue creation UI
 */
function initializeQueueCreation() {
  const newQueueBtn = document.getElementById('new-queue-btn');
  const cancelCreationBtn = document.getElementById('cancel-creation-btn');
  const saveQueueBtn = document.getElementById('save-queue-btn');
  const queueTitleInput = document.getElementById('queue-title-input');

  // New Queue button
  if (newQueueBtn) {
    console.log('🎯 New queue button found, adding click listener');
    newQueueBtn.addEventListener('click', function() {
      console.log('🖱️ New queue button clicked');
      if (typeof enterQueueCreationMode === 'function') {
        console.log('✅ enterQueueCreationMode function found, calling it');
        enterQueueCreationMode();
      } else {
        console.error('❌ enterQueueCreationMode function not found');
      }
    });
  } else {
    console.warn('⚠️ New queue button element not found');
  }

  // Cancel Creation button
  if (cancelCreationBtn) {
    cancelCreationBtn.addEventListener('click', function() {
      if (typeof exitQueueCreationMode === 'function') {
        exitQueueCreationMode();
      }
    });
  }

  // Save Queue button
  if (saveQueueBtn) {
    saveQueueBtn.addEventListener('click', async function() {
      const titleInput = document.getElementById('queue-title-input');
      const title = titleInput ? titleInput.value.trim() : '';

      if (!title) {
        console.log('No queue title entered');
        return;
      }

      const draftQueue = (typeof getDraftQueue === 'function') ? getDraftQueue() : [];
      if (draftQueue.length === 0) {
        console.log('No videos in draft queue');
        return;
      }

      // Set loading state
      setSaveQueueButtonLoading(saveQueueBtn, true);

      try {
        // Save the draft queue as a personal queue
        if (typeof savePersonalQueueFromDraft === 'function') {
          const queueId = await savePersonalQueueFromDraft(title, draftQueue);
          if (queueId) {
            if (typeof exitQueueCreationMode === 'function') {
              exitQueueCreationMode();
            }

            // Show success notification with options
            showQueueCreatedNotification(title, queueId, draftQueue.length);
          }
        } else {
          console.log('Queue creation functionality not available');
        }
      } catch (error) {
        console.error('Error saving queue:', error);
        console.log('Failed to save queue:', error);
      } finally {
        // Reset loading state
        setSaveQueueButtonLoading(saveQueueBtn, false);
      }
    });
  }

  // Queue title input validation
  if (queueTitleInput) {
    queueTitleInput.addEventListener('input', function() {
      if (typeof updateQueueCreationForm === 'function') {
        updateQueueCreationForm();
      }
    });

    // Enter key to save
    queueTitleInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && saveQueueBtn && !saveQueueBtn.disabled) {
        saveQueueBtn.click();
      }
    });
  }

  console.log('✅ Queue creation UI initialized');
}

/**
 * Dismiss notification with animation
 */
function dismissNotification(notification) {
  if (!notification || !notification.parentNode) return;

  notification.style.animation = 'slideOutToRight 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 300);
}

/**
 * Show queue created notification with options
 */
function showQueueCreatedNotification(title, queueId, videoCount) {
  // Create a more elaborate success message with options
  const messageContainer = document.getElementById('message-container');
  if (!messageContainer) {
    console.log('Queue created successfully:', title);
    return;
  }

  const notification = document.createElement('div');
  notification.className = 'queue-created-notification';
  notification.innerHTML = `
    <div class="notification-content">
      <div class="notification-header">
        <div class="notification-icon">✨</div>
        <div class="notification-text">
          <h4>Queue Created Successfully!</h4>
          <p>"${title}" with ${videoCount} video${videoCount !== 1 ? 's' : ''}</p>
        </div>
      </div>
      <div class="notification-actions">
        <button class="notification-btn play-btn" data-queue-id="${queueId}">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z"/>
          </svg>
          Play Now
        </button>
        <button class="notification-btn dismiss-btn">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
          Dismiss
        </button>
      </div>
    </div>
  `;

  // Add event listeners
  const playBtn = notification.querySelector('.play-btn');
  const dismissBtn = notification.querySelector('.dismiss-btn');

  if (playBtn) {
    playBtn.addEventListener('click', async function() {
      const queueId = this.getAttribute('data-queue-id');
      if (typeof loadPersonalQueueAndPlay === 'function') {
        await loadPersonalQueueAndPlay(queueId);
      }
      dismissNotification(notification);
    });
  }

  if (dismissBtn) {
    dismissBtn.addEventListener('click', function() {
      dismissNotification(notification);
    });
  }

  // Auto-dismiss after 8 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      dismissNotification(notification);
    }
  }, 8000);

  messageContainer.appendChild(notification);
}

/**
 * Initialize all UI interactions
 */
function initializeUIInteractions() {
  console.log('🎛️ Initializing UI interactions...');

  initializeUnifiedInput();
  initializePlayerControls();
  initializeQueueSharing();
  initializeKeyboardShortcuts();
  initializeQueueBrowser();
  initializeQueueCreation();

  // Initialize queue link copy functionality
  if (typeof initializeQueueLinkCopy === 'function') {
    initializeQueueLinkCopy();
  }

  // Check if URL contains a queue to load
  checkURLForQueue();

  console.log('✅ UI interactions initialized');
}

console.log('✅ UI Interactions module loaded');
